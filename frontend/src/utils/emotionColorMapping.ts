/**
 * Comprehensive pastel emotion-to-color mapping for all 48 Hume emotions
 * Designed for calming, elegant UI/UX with smooth transitions
 */

export interface EmotionColorScheme {
  primary: string;    // Main RGB color
  secondary: string;  // Secondary RGB color
  accent: string;     // Accent RGB color
  glow: string;       // Glow effect with RGBA
  intensity: number;  // 0.0 - 1.0
  vibrance: number;   // 0.0 - 1.0
}

// Complete mapping for all 48 Hume emotions with calming pastel colors
export const HUME_EMOTION_COLORS: Record<string, EmotionColorScheme> = {
  // Positive High-Energy Emotions - Warm pastels
  admiration: {
    primary: 'rgb(255, 228, 225)', // Misty rose
    secondary: 'rgb(255, 218, 185)', // Peach puff
    accent: 'rgb(255, 239, 213)', // Papaya whip
    glow: 'rgba(255, 105, 180, 0.3)', // Hot pink glow
    intensity: 0.7,
    vibrance: 0.8
  },
  
  adoration: {
    primary: 'rgb(255, 182, 193)', // Light pink
    secondary: 'rgb(255, 192, 203)', // Pink
    accent: 'rgb(255, 228, 225)', // Misty rose
    glow: 'rgba(255, 20, 147, 0.35)', // Deep pink glow
    intensity: 0.8,
    vibrance: 0.9
  },

  aestheticAppreciation: {
    primary: 'rgb(230, 230, 250)', // Lavender
    secondary: 'rgb(221, 160, 221)', // Plum
    accent: 'rgb(255, 240, 245)', // Lavender blush
    glow: 'rgba(147, 112, 219, 0.3)', // Medium purple glow
    intensity: 0.6,
    vibrance: 0.7
  },

  amusement: {
    primary: 'rgb(255, 255, 224)', // Light yellow
    secondary: 'rgb(255, 239, 213)', // Papaya whip
    accent: 'rgb(255, 228, 196)', // Bisque
    glow: 'rgba(255, 255, 0, 0.25)', // Yellow glow
    intensity: 0.7,
    vibrance: 0.8
  },

  joy: {
    primary: 'rgb(255, 248, 220)', // Cornsilk
    secondary: 'rgb(255, 239, 213)', // Papaya whip
    accent: 'rgb(255, 228, 196)', // Bisque
    glow: 'rgba(255, 215, 0, 0.3)', // Gold glow
    intensity: 0.8,
    vibrance: 0.9
  },

  excitement: {
    primary: 'rgb(255, 228, 225)', // Misty rose
    secondary: 'rgb(255, 218, 185)', // Peach puff
    accent: 'rgb(255, 192, 203)', // Pink
    glow: 'rgba(255, 69, 0, 0.25)', // Orange red glow
    intensity: 0.9,
    vibrance: 0.95
  },

  ecstasy: {
    primary: 'rgb(255, 182, 193)', // Light pink
    secondary: 'rgb(255, 160, 122)', // Light salmon
    accent: 'rgb(255, 218, 185)', // Peach puff
    glow: 'rgba(255, 20, 147, 0.4)', // Deep pink glow
    intensity: 1.0,
    vibrance: 1.0
  },

  triumph: {
    primary: 'rgb(255, 215, 0)', // Gold
    secondary: 'rgb(255, 228, 181)', // Moccasin
    accent: 'rgb(255, 218, 185)', // Peach puff
    glow: 'rgba(255, 140, 0, 0.4)', // Dark orange glow
    intensity: 0.9,
    vibrance: 0.95
  },

  // Positive Medium-Energy Emotions - Soft pastels
  contentment: {
    primary: 'rgb(240, 248, 255)', // Alice blue
    secondary: 'rgb(230, 230, 250)', // Lavender
    accent: 'rgb(255, 240, 245)', // Lavender blush
    glow: 'rgba(173, 216, 230, 0.3)', // Light blue glow
    intensity: 0.5,
    vibrance: 0.6
  },

  satisfaction: {
    primary: 'rgb(255, 228, 225)', // Misty rose
    secondary: 'rgb(255, 218, 185)', // Peach puff
    accent: 'rgb(255, 239, 213)', // Papaya whip
    glow: 'rgba(255, 182, 193, 0.3)', // Light pink glow
    intensity: 0.6,
    vibrance: 0.7
  },

  relief: {
    primary: 'rgb(245, 255, 250)', // Mint cream
    secondary: 'rgb(240, 255, 240)', // Honeydew
    accent: 'rgb(240, 248, 255)', // Alice blue
    glow: 'rgba(144, 238, 144, 0.25)', // Light green glow
    intensity: 0.4,
    vibrance: 0.5
  },

  pride: {
    primary: 'rgb(255, 215, 0)', // Gold
    secondary: 'rgb(255, 228, 181)', // Moccasin
    accent: 'rgb(255, 239, 213)', // Papaya whip
    glow: 'rgba(255, 215, 0, 0.35)', // Gold glow
    intensity: 0.7,
    vibrance: 0.8
  },

  love: {
    primary: 'rgb(255, 182, 193)', // Light pink
    secondary: 'rgb(255, 192, 203)', // Pink
    accent: 'rgb(255, 228, 225)', // Misty rose
    glow: 'rgba(255, 105, 180, 0.4)', // Hot pink glow
    intensity: 0.8,
    vibrance: 0.9
  },

  romance: {
    primary: 'rgb(255, 192, 203)', // Pink
    secondary: 'rgb(255, 182, 193)', // Light pink
    accent: 'rgb(255, 228, 225)', // Misty rose
    glow: 'rgba(255, 20, 147, 0.35)', // Deep pink glow
    intensity: 0.7,
    vibrance: 0.8
  },

  // Positive Low-Energy Emotions - Very soft pastels
  calmness: {
    primary: 'rgb(240, 248, 255)', // Alice blue
    secondary: 'rgb(245, 245, 220)', // Beige
    accent: 'rgb(255, 255, 240)', // Ivory
    glow: 'rgba(176, 196, 222, 0.2)', // Light steel blue glow
    intensity: 0.3,
    vibrance: 0.4
  },

  // Neutral/Cognitive Emotions - Cool pastels
  concentration: {
    primary: 'rgb(230, 230, 250)', // Lavender
    secondary: 'rgb(240, 248, 255)', // Alice blue
    accent: 'rgb(248, 248, 255)', // Ghost white
    glow: 'rgba(123, 104, 238, 0.25)', // Medium slate blue glow
    intensity: 0.6,
    vibrance: 0.7
  },

  contemplation: {
    primary: 'rgb(221, 160, 221)', // Plum
    secondary: 'rgb(230, 230, 250)', // Lavender
    accent: 'rgb(255, 240, 245)', // Lavender blush
    glow: 'rgba(147, 112, 219, 0.3)', // Medium purple glow
    intensity: 0.5,
    vibrance: 0.6
  },

  interest: {
    primary: 'rgb(173, 216, 230)', // Light blue
    secondary: 'rgb(240, 248, 255)', // Alice blue
    accent: 'rgb(230, 230, 250)', // Lavender
    glow: 'rgba(70, 130, 180, 0.3)', // Steel blue glow
    intensity: 0.6,
    vibrance: 0.7
  },

  realization: {
    primary: 'rgb(255, 255, 224)', // Light yellow
    secondary: 'rgb(255, 248, 220)', // Cornsilk
    accent: 'rgb(255, 239, 213)', // Papaya whip
    glow: 'rgba(255, 215, 0, 0.25)', // Gold glow
    intensity: 0.7,
    vibrance: 0.8
  },

  // Negative emotions - Muted, calming versions
  anger: {
    primary: 'rgb(255, 228, 225)', // Very light pink (calmed red)
    secondary: 'rgb(255, 218, 185)', // Peach puff
    accent: 'rgb(255, 239, 213)', // Papaya whip
    glow: 'rgba(255, 99, 71, 0.2)', // Tomato glow (very muted)
    intensity: 0.4,
    vibrance: 0.5
  },

  anxiety: {
    primary: 'rgb(230, 230, 250)', // Lavender (calming)
    secondary: 'rgb(240, 248, 255)', // Alice blue
    accent: 'rgb(255, 240, 245)', // Lavender blush
    glow: 'rgba(138, 43, 226, 0.15)', // Blue violet glow (very soft)
    intensity: 0.3,
    vibrance: 0.4
  },

  sadness: {
    primary: 'rgb(173, 216, 230)', // Light blue (soft)
    secondary: 'rgb(240, 248, 255)', // Alice blue
    accent: 'rgb(230, 230, 250)', // Lavender
    glow: 'rgba(70, 130, 180, 0.2)', // Steel blue glow (muted)
    intensity: 0.4,
    vibrance: 0.5
  },

  fear: {
    primary: 'rgb(230, 230, 250)', // Lavender
    secondary: 'rgb(221, 160, 221)', // Plum
    accent: 'rgb(255, 240, 245)', // Lavender blush
    glow: 'rgba(75, 0, 130, 0.15)', // Indigo glow (very soft)
    intensity: 0.3,
    vibrance: 0.4
  },

  // Additional emotions with calming pastels
  confusion: {
    primary: 'rgb(221, 160, 221)', // Plum
    secondary: 'rgb(230, 230, 250)', // Lavender
    accent: 'rgb(240, 248, 255)', // Alice blue
    glow: 'rgba(147, 112, 219, 0.2)', // Medium purple glow (soft)
    intensity: 0.4,
    vibrance: 0.5
  },

  surprise: {
    primary: 'rgb(255, 255, 224)', // Light yellow
    secondary: 'rgb(255, 248, 220)', // Cornsilk
    accent: 'rgb(255, 239, 213)', // Papaya whip
    glow: 'rgba(255, 255, 0, 0.25)', // Yellow glow
    intensity: 0.6,
    vibrance: 0.7
  },

  surprisePositive: {
    primary: 'rgb(255, 248, 220)', // Cornsilk
    secondary: 'rgb(255, 239, 213)', // Papaya whip
    accent: 'rgb(255, 228, 196)', // Bisque
    glow: 'rgba(255, 215, 0, 0.3)', // Gold glow
    intensity: 0.7,
    vibrance: 0.8
  },

  surpriseNegative: {
    primary: 'rgb(230, 230, 250)', // Lavender
    secondary: 'rgb(240, 248, 255)', // Alice blue
    accent: 'rgb(255, 240, 245)', // Lavender blush
    glow: 'rgba(123, 104, 238, 0.2)', // Medium slate blue glow (soft)
    intensity: 0.4,
    vibrance: 0.5
  },

  // Additional Hume emotions with calming pastels
  awe: {
    primary: 'rgb(221, 160, 221)', // Plum
    secondary: 'rgb(230, 230, 250)', // Lavender
    accent: 'rgb(255, 240, 245)', // Lavender blush
    glow: 'rgba(147, 112, 219, 0.35)', // Medium purple glow
    intensity: 0.8,
    vibrance: 0.9
  },

  awkwardness: {
    primary: 'rgb(255, 228, 225)', // Misty rose (soft)
    secondary: 'rgb(255, 239, 213)', // Papaya whip
    accent: 'rgb(255, 248, 220)', // Cornsilk
    glow: 'rgba(255, 182, 193, 0.2)', // Light pink glow (very soft)
    intensity: 0.3,
    vibrance: 0.4
  },

  boredom: {
    primary: 'rgb(245, 245, 220)', // Beige
    secondary: 'rgb(240, 248, 255)', // Alice blue
    accent: 'rgb(255, 255, 240)', // Ivory
    glow: 'rgba(192, 192, 192, 0.15)', // Silver glow (very muted)
    intensity: 0.2,
    vibrance: 0.3
  },

  contempt: {
    primary: 'rgb(230, 230, 250)', // Lavender (neutralized)
    secondary: 'rgb(240, 248, 255)', // Alice blue
    accent: 'rgb(255, 240, 245)', // Lavender blush
    glow: 'rgba(75, 0, 130, 0.15)', // Indigo glow (very soft)
    intensity: 0.3,
    vibrance: 0.4
  },

  craving: {
    primary: 'rgb(255, 218, 185)', // Peach puff
    secondary: 'rgb(255, 228, 196)', // Bisque
    accent: 'rgb(255, 239, 213)', // Papaya whip
    glow: 'rgba(255, 140, 0, 0.25)', // Dark orange glow (soft)
    intensity: 0.6,
    vibrance: 0.7
  },

  desire: {
    primary: 'rgb(255, 192, 203)', // Pink
    secondary: 'rgb(255, 182, 193)', // Light pink
    accent: 'rgb(255, 228, 225)', // Misty rose
    glow: 'rgba(255, 105, 180, 0.3)', // Hot pink glow
    intensity: 0.7,
    vibrance: 0.8
  },

  determination: {
    primary: 'rgb(255, 215, 0)', // Gold
    secondary: 'rgb(255, 228, 181)', // Moccasin
    accent: 'rgb(255, 239, 213)', // Papaya whip
    glow: 'rgba(255, 140, 0, 0.35)', // Dark orange glow
    intensity: 0.8,
    vibrance: 0.9
  },

  disappointment: {
    primary: 'rgb(173, 216, 230)', // Light blue (soft)
    secondary: 'rgb(240, 248, 255)', // Alice blue
    accent: 'rgb(230, 230, 250)', // Lavender
    glow: 'rgba(70, 130, 180, 0.2)', // Steel blue glow (muted)
    intensity: 0.4,
    vibrance: 0.5
  },

  disgust: {
    primary: 'rgb(245, 255, 250)', // Mint cream (neutralized)
    secondary: 'rgb(240, 255, 240)', // Honeydew
    accent: 'rgb(240, 248, 255)', // Alice blue
    glow: 'rgba(144, 238, 144, 0.15)', // Light green glow (very soft)
    intensity: 0.3,
    vibrance: 0.4
  },

  distress: {
    primary: 'rgb(230, 230, 250)', // Lavender (calming)
    secondary: 'rgb(240, 248, 255)', // Alice blue
    accent: 'rgb(255, 240, 245)', // Lavender blush
    glow: 'rgba(138, 43, 226, 0.15)', // Blue violet glow (very soft)
    intensity: 0.3,
    vibrance: 0.4
  },

  doubt: {
    primary: 'rgb(221, 160, 221)', // Plum
    secondary: 'rgb(230, 230, 250)', // Lavender
    accent: 'rgb(240, 248, 255)', // Alice blue
    glow: 'rgba(147, 112, 219, 0.2)', // Medium purple glow (soft)
    intensity: 0.4,
    vibrance: 0.5
  },

  embarrassment: {
    primary: 'rgb(255, 228, 225)', // Misty rose (very soft)
    secondary: 'rgb(255, 239, 213)', // Papaya whip
    accent: 'rgb(255, 248, 220)', // Cornsilk
    glow: 'rgba(255, 182, 193, 0.2)', // Light pink glow (very soft)
    intensity: 0.3,
    vibrance: 0.4
  },

  empathicPain: {
    primary: 'rgb(230, 230, 250)', // Lavender (comforting)
    secondary: 'rgb(240, 248, 255)', // Alice blue
    accent: 'rgb(255, 240, 245)', // Lavender blush
    glow: 'rgba(123, 104, 238, 0.2)', // Medium slate blue glow (soft)
    intensity: 0.4,
    vibrance: 0.5
  },

  entrancement: {
    primary: 'rgb(221, 160, 221)', // Plum
    secondary: 'rgb(230, 230, 250)', // Lavender
    accent: 'rgb(255, 240, 245)', // Lavender blush
    glow: 'rgba(147, 112, 219, 0.4)', // Medium purple glow
    intensity: 0.8,
    vibrance: 0.9
  },

  envy: {
    primary: 'rgb(245, 255, 250)', // Mint cream (neutralized)
    secondary: 'rgb(240, 255, 240)', // Honeydew
    accent: 'rgb(240, 248, 255)', // Alice blue
    glow: 'rgba(144, 238, 144, 0.2)', // Light green glow (soft)
    intensity: 0.3,
    vibrance: 0.4
  },

  guilt: {
    primary: 'rgb(230, 230, 250)', // Lavender (calming)
    secondary: 'rgb(240, 248, 255)', // Alice blue
    accent: 'rgb(255, 240, 245)', // Lavender blush
    glow: 'rgba(138, 43, 226, 0.15)', // Blue violet glow (very soft)
    intensity: 0.3,
    vibrance: 0.4
  },

  horror: {
    primary: 'rgb(230, 230, 250)', // Lavender (neutralized)
    secondary: 'rgb(240, 248, 255)', // Alice blue
    accent: 'rgb(255, 240, 245)', // Lavender blush
    glow: 'rgba(75, 0, 130, 0.15)', // Indigo glow (very soft)
    intensity: 0.3,
    vibrance: 0.4
  },

  nostalgia: {
    primary: 'rgb(255, 239, 213)', // Papaya whip
    secondary: 'rgb(255, 228, 196)', // Bisque
    accent: 'rgb(255, 248, 220)', // Cornsilk
    glow: 'rgba(218, 165, 32, 0.25)', // Goldenrod glow (soft)
    intensity: 0.5,
    vibrance: 0.6
  },

  pain: {
    primary: 'rgb(230, 230, 250)', // Lavender (comforting)
    secondary: 'rgb(240, 248, 255)', // Alice blue
    accent: 'rgb(255, 240, 245)', // Lavender blush
    glow: 'rgba(123, 104, 238, 0.2)', // Medium slate blue glow (soft)
    intensity: 0.3,
    vibrance: 0.4
  },

  shame: {
    primary: 'rgb(255, 228, 225)', // Misty rose (very soft)
    secondary: 'rgb(255, 239, 213)', // Papaya whip
    accent: 'rgb(255, 248, 220)', // Cornsilk
    glow: 'rgba(255, 182, 193, 0.15)', // Light pink glow (very soft)
    intensity: 0.3,
    vibrance: 0.4
  },

  sympathy: {
    primary: 'rgb(240, 248, 255)', // Alice blue (comforting)
    secondary: 'rgb(230, 230, 250)', // Lavender
    accent: 'rgb(255, 240, 245)', // Lavender blush
    glow: 'rgba(173, 216, 230, 0.3)', // Light blue glow
    intensity: 0.6,
    vibrance: 0.7
  },

  tiredness: {
    primary: 'rgb(245, 245, 220)', // Beige
    secondary: 'rgb(240, 248, 255)', // Alice blue
    accent: 'rgb(255, 255, 240)', // Ivory
    glow: 'rgba(192, 192, 192, 0.2)', // Silver glow (very muted)
    intensity: 0.2,
    vibrance: 0.3
  }
};

/**
 * Blends multiple emotions based on their intensity values
 * Returns a single color scheme representing the dominant emotional state
 */
export function blendEmotionColors(emotions: Record<string, number>): EmotionColorScheme {
  // Default neutral state
  const defaultScheme: EmotionColorScheme = {
    primary: 'rgb(248, 248, 255)', // Ghost white
    secondary: 'rgb(245, 245, 220)', // Beige
    accent: 'rgb(255, 255, 240)', // Ivory
    glow: 'rgba(192, 192, 192, 0.1)', // Very soft silver glow
    intensity: 0.3,
    vibrance: 0.4
  };

  if (!emotions || Object.keys(emotions).length === 0) {
    return defaultScheme;
  }

  // Find the dominant emotion (highest value)
  let dominantEmotion = '';
  let maxIntensity = 0;

  for (const [emotion, value] of Object.entries(emotions)) {
    if (value > maxIntensity && HUME_EMOTION_COLORS[emotion]) {
      maxIntensity = value;
      dominantEmotion = emotion;
    }
  }

  if (!dominantEmotion || maxIntensity < 0.1) {
    return defaultScheme;
  }

  const dominantScheme = HUME_EMOTION_COLORS[dominantEmotion];

  // Blend with secondary emotions if they're significant
  const secondaryEmotions = Object.entries(emotions)
    .filter(([emotion, value]) => emotion !== dominantEmotion && value > 0.2 && HUME_EMOTION_COLORS[emotion])
    .sort(([, a], [, b]) => b - a)
    .slice(0, 2); // Take top 2 secondary emotions

  if (secondaryEmotions.length === 0) {
    return {
      ...dominantScheme,
      intensity: dominantScheme.intensity * maxIntensity,
      vibrance: dominantScheme.vibrance * maxIntensity
    };
  }

  // Simple blending - use dominant emotion as base, modulate with secondary
  const blendedScheme = { ...dominantScheme };

  // Adjust intensity and vibrance based on emotional complexity
  const totalSecondaryIntensity = secondaryEmotions.reduce((sum, [, value]) => sum + value, 0);
  blendedScheme.intensity = Math.min(1.0, dominantScheme.intensity * maxIntensity + totalSecondaryIntensity * 0.1);
  blendedScheme.vibrance = Math.min(1.0, dominantScheme.vibrance * maxIntensity + totalSecondaryIntensity * 0.05);

  return blendedScheme;
}

/**
 * Converts RGB string to individual color components
 */
export function parseRgbColor(rgbString: string): { r: number; g: number; b: number } {
  const match = rgbString.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
  if (!match) {
    return { r: 248, g: 248, b: 255 }; // Default to ghost white
  }
  return {
    r: parseInt(match[1]),
    g: parseInt(match[2]),
    b: parseInt(match[3])
  };
}

/**
 * Generates a smooth background gradient based on emotion colors
 */
export function generateEmotionBackground(colorScheme: EmotionColorScheme): string {
  const primary = parseRgbColor(colorScheme.primary);
  const secondary = parseRgbColor(colorScheme.secondary);

  // Create a very subtle gradient for the background
  const alpha = 0.05 + (colorScheme.intensity * 0.1); // Very subtle

  return `linear-gradient(135deg,
    rgba(${primary.r}, ${primary.g}, ${primary.b}, ${alpha}) 0%,
    rgba(${secondary.r}, ${secondary.g}, ${secondary.b}, ${alpha * 0.7}) 50%,
    rgba(255, 255, 255, 1) 100%)`;
}

/**
 * Gets the appropriate orb colors based on emotion state
 */
export function getOrbColors(colorScheme: EmotionColorScheme): {
  primary: string;
  secondary: string;
  glow: string;
} {
  return {
    primary: colorScheme.primary,
    secondary: colorScheme.secondary,
    glow: colorScheme.glow
  };
}
